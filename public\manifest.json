{"short_name": "Chat App", "name": "Chat Application", "description": "A real-time chat application built with React and Firebase", "icons": [{"src": "favicon.ico", "sizes": "64x64 32x32 24x24 16x16", "type": "image/x-icon"}, {"src": "logo192.png", "type": "image/png", "sizes": "192x192", "purpose": "any maskable"}, {"src": "logo512.png", "type": "image/png", "sizes": "512x512", "purpose": "any maskable"}], "start_url": "/", "display": "standalone", "orientation": "portrait-primary", "theme_color": "#000000", "background_color": "#ffffff", "scope": "/", "lang": "en", "dir": "ltr"}