{"name": "chat-app", "version": "0.1.0", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "firebase": "^8.1.2", "react": "^18.2.0", "react-avatar-editor": "^13.0.0", "react-dom": "^18.2.0", "react-mic": "^12.4.6", "react-router": "^5.2.0", "react-router-dom": "^5.2.0", "react-scripts": "5.0.1", "rsuite": "^4.10.2", "sass": "^1.66.1", "timeago-react": "^3.0.6", "use-context-selector": "^1.4.1", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src/**/*.js", "format": "prettier --write src/**/*.js", "format:lint": "npm run format && npm run lint", "clear": "<PERSON><PERSON><PERSON> build", "vercel-build": "react-scripts build"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"babel-eslint": "^10.1.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-prettier": "^9.0.0", "eslint-plugin-import": "^2.28.1", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "prettier": "^3.0.3", "rimraf": "^5.0.1"}}