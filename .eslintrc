{"extends": ["eslint:recommended", "plugin:react/recommended", "prettier"], "plugins": ["react-hooks"], "parser": "@babel/eslint-parser", "env": {"browser": true, "es6": true, "node": true}, "parserOptions": {"ecmaVersion": 2021, "ecmaFeatures": {"jsx": true}, "sourceType": "module", "requireConfigFile": false, "babelOptions": {"presets": ["@babel/preset-react"]}}, "settings": {"react": {"version": "detect"}}, "rules": {"react/prop-types": "off", "react-hooks/rules-of-hooks": "error", "react-hooks/exhaustive-deps": "warn", "no-unused-vars": "warn"}}