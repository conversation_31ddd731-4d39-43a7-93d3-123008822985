{"version": 2, "builds": [{"src": "package.json", "use": "@vercel/static-build", "config": {"distDir": "build"}}], "routes": [{"src": "/static/(.*)", "headers": {"cache-control": "s-maxage=31536000,immutable"}}, {"src": "/manifest.json", "dest": "/manifest.json", "headers": {"content-type": "application/manifest+json", "cache-control": "public, max-age=0, must-revalidate"}}, {"src": "/Firebase-messaging-sw.js", "dest": "/Firebase-messaging-sw.js", "headers": {"content-type": "application/javascript", "service-worker-allowed": "/"}}, {"src": "/(favicon\\.ico|logo192\\.png|logo512\\.png|robots\\.txt)$", "dest": "/$1"}, {"src": "/(.*)", "dest": "/index.html"}], "env": {"GENERATE_SOURCEMAP": "false"}}